import type { BaptismaRecordFilterInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useState } from 'react'
import { GET_BAPTISMA_RECORD_LIST } from '~/graphql/queries/get-baptisma-record-list'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetBaptismaRecord(enabled: boolean) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1).withOptions({ history: 'push' }))

  const [activeBaptismaFilter, setActiveBaptismaFilter] = useState<BaptismaRecordFilterInput | null>(null)

  const searchBaptisma = (data: BaptismaRecordFilterInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveBaptismaFilter(data)
  }

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-baptisma-record', page, activeBaptismaFilter],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_BAPTISMA_RECORD_LIST,
        variables: {
          first: 1,
          page,
          baptisma_filter: activeBaptismaFilter,
        },
      })
    },
    enabled,
  })

  const lastPage = data?.getBaptismaRecordList?.paginator_info?.last_page ?? 1

  return { data, isLoading, isError, page, handlePage, lastPage, searchBaptisma }
}
