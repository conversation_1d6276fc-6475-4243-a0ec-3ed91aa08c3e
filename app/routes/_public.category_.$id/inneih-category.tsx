import type { InneihRecordFilterInput } from '~/gql/graphql'
import { SearchIcon, X } from 'lucide-react'
import { parseAsBoolean, parseAsString, useQueryState } from 'nuqs'
import { useEffect } from 'react'
import { Accordion, AccordionContent, AccordionItem } from '~/components/ui/accordion'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'

interface Props {
  searchInneih: (data: InneihRecordFilterInput | null) => void
  isLoading: boolean
}

export default function InneihCategory({ searchInneih, isLoading }: Props) {
  // URL state for search accordion
  const [isSearchOpen, setIsSearchOpen] = useQueryState('search_open', parseAsBoolean.withDefault(false))

  // URL state for form fields
  const [registrationNo, setRegistrationNo] = useQueryState('registration_no', parseAsString.withDefault(''))
  const [mipaHming, setMipaHming] = useQueryState('mipa_hming', parseAsString.withDefault(''))
  const [mipaPaHming, setMipaPaHming] = useQueryState('mipa_pa_hming', parseAsString.withDefault(''))
  const [hmeichheHming, setHmeichheHming] = useQueryState('hmeichhe_hming', parseAsString.withDefault(''))
  const [hmeichhePaHming, setHmeichhePaHming] = useQueryState('hmeichhe_pa_hming', parseAsString.withDefault(''))
  const [inneihNi, setInneihNi] = useQueryState('inneih_ni', parseAsString.withDefault(''))
  const [hmun, setHmun] = useQueryState('hmun', parseAsString.withDefault(''))
  const [inneihtirtu, setInneihtirtu] = useQueryState('inneihtirtu', parseAsString.withDefault(''))

  const form = useAppForm({
    defaultValues: {
      registration_no: registrationNo,
      mipa_hming: mipaHming,
      mipa_pa_hming: mipaPaHming,
      hmeichhe_hming: hmeichheHming,
      hmeichhe_pa_hming: hmeichhePaHming,
      inneih_ni: inneihNi,
      hmun,
      inneihtirtu,
    },
    onSubmit: async ({ value }) => {
      // Update URL state with form values
      setRegistrationNo(value.registration_no || '')
      setMipaHming(value.mipa_hming || '')
      setMipaPaHming(value.mipa_pa_hming || '')
      setHmeichheHming(value.hmeichhe_hming || '')
      setHmeichhePaHming(value.hmeichhe_pa_hming || '')
      setInneihNi(value.inneih_ni || '')
      setHmun(value.hmun || '')
      setInneihtirtu(value.inneihtirtu || '')

      const isEmpty = Object.values(value).every(val => !val || val === '')
      searchInneih(isEmpty ? null : value)
    },
  })

  useEffect(() => {
    const urlValues = {
      registration_no: registrationNo,
      mipa_hming: mipaHming,
      mipa_pa_hming: mipaPaHming,
      hmeichhe_hming: hmeichheHming,
      hmeichhe_pa_hming: hmeichhePaHming,
      inneih_ni: inneihNi,
      hmun,
      inneihtirtu,
    }

    // Check if any URL parameters have values
    const hasUrlValues = Object.values(urlValues).some(val => val !== '')

    if (hasUrlValues) {
      setIsSearchOpen(true)
      searchInneih(urlValues)
    }
  }, [registrationNo, mipaHming, mipaPaHming, hmeichheHming, hmeichhePaHming, inneihNi, hmun, inneihtirtu, searchInneih, setIsSearchOpen])

  return (
    <Card className="my-4 bg-white">
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <Breadcrumb className="pt-4 pb-4">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  /
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  Inneih Records
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          {isSearchOpen
            ? (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => setIsSearchOpen(false)}
                >
                  <X className="text-xl" />
                </Button>
              )
            : (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => setIsSearchOpen(true)}
                >
                  <SearchIcon className="text-xl" />
                </Button>
              )}
        </div>
        <Accordion
          type="single"
          value={isSearchOpen ? 'search-form' : ''}
          className="border-none"
        >
          <AccordionItem value="search-form" className="border-none">
            <AccordionContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  form.handleSubmit()
                }}
                className="grid grid-cols-4 gap-4 pt-4"
              >
                <form.AppField
                  name="registration_no"
                  children={field => <field.InputField label="Registration No" />}
                />
                <form.AppField
                  name="mipa_hming"
                  children={field => <field.InputField label="Mipa hming" />}
                />
                <form.AppField
                  name="mipa_pa_hming"
                  children={field => <field.InputField label="Mipa Pa hming" />}
                />
                <form.AppField
                  name="hmeichhe_hming"
                  children={field => <field.InputField label="Hmeichhe hming" />}
                />

                <form.AppField
                  name="hmeichhe_pa_hming"
                  children={field => <field.InputField label="Hmeichhe nu hming" />}
                />
                <form.AppField
                  name="inneih_ni"
                  children={field => <field.InputField label="Inneih ni" type="date" />}
                />
                <form.AppField
                  name="hmun"
                  children={field => <field.InputField label="Hmun" />}
                />
                <form.AppField
                  name="inneihtirtu"
                  children={field => <field.InputField label="Inneih tir tu" />}
                />
                <div className="col-span-1">
                  <Button type="submit" isLoading={isLoading}>
                    Search
                  </Button>
                </div>
              </form>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}
